fetch("http://**************:20051/NOCE/portal/pages_util_ProgressbarTwo_submitCmd.action", {
  "headers": {
    "accept": "application/json, text/javascript, */*; q=0.01",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    "proxy-connection": "keep-alive",
    "x-requested-with": "XMLHttpRequest"
  },
  "referrer": "http://**************:20051/NOCE/portal/pages_index_Index_home.action?appId=OneMoreCase&menuId=346&perId=912&id_path=new&isRedirect=true&appName=%E4%B8%80%E7%82%B9%E4%B8%80%E6%A1%88",
  "body": "cmd=%5B%22OneMoreCase_getRqManageIdsByCity%22%2C%22COND_LIST%3A%7B%5C%22city%5C%22%3A%5B%5C%22%E6%8F%AD%E9%98%B3%5C%22%5D%7D%22%5D&database=3&appId=OneMoreCase&appName=&dbName=",
  "method": "POST",
  "mode": "cors",
  "credentials": "include"
});